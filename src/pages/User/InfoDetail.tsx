import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams, useNavigate } from "react-router";
import toast from "react-hot-toast";
import PageBreadcrumb from "@components/common/PageBreadCrumb";
import PageMeta from "@components/common/PageMeta";
import ComponentCard from "@components/common/ComponentCard";
import Label from "@components/form/Label";
import Badge from "@components/ui/badge/Badge";
import Button from "@components/ui/button/Button";
import { AngleLeftIcon } from "@assets/icons";
import { useCopyToClipboard } from "@hooks/useCopyToClipboard";
import { ROUTES } from "@/constants/routes";

// 用户详情数据类型定义
interface UserDetailData {
  id: string;
  avatar: string;
  username: string;
  userId: string;
  userEmail: string;
  phone: string;
  region: string;
  registerSource: string;
  registerTime: string;
  status: "active" | "inactive";
  // 内用信息
  storageUsed: string;
  storageLimit: string;
  personalSpaceUsed: string;
  personalSpaceLimit: string;
  // 团队信息
  teamStatus: string;
  teamExpiry: string;
  teamId: string;
  teamCapacity: string;
}

// 模拟用户详情数据 - 根据不同的用户ID返回不同的数据
const getMockUserDetailData = (userId: string): UserDetailData | null => {
  const userDataMap: Record<string, UserDetailData> = {
    UID3232184: {
      id: "UID3232184",
      avatar: "/images/user/user-01.jpg",
      username: "昱云",
      userId: "UID3232184",
      userEmail: "<EMAIL>",
      phone: "173****165",
      region: "中国",
      registerSource: "邀请码",
      registerTime: "2025-07-18 14:25:43",
      status: "active",
      storageUsed: "0 MB",
      storageLimit: "永久有效",
      personalSpaceUsed: "0 MB",
      personalSpaceLimit: "500 MB",
      teamStatus: "未创建",
      teamExpiry: "-",
      teamId: "无",
      teamCapacity: "无",
    },
    UID3232183: {
      id: "UID3232183",
      avatar: "/images/user/user-02.jpg",
      username: "张三",
      userId: "UID3232183",
      userEmail: "<EMAIL>",
      phone: "138****888",
      region: "新加坡",
      registerSource: "邀请码",
      registerTime: "2025-07-18 14:25:43",
      status: "active",
      storageUsed: "0 MB",
      storageLimit: "永久有效",
      personalSpaceUsed: "0 MB",
      personalSpaceLimit: "500 MB",
      teamStatus: "未创建",
      teamExpiry: "-",
      teamId: "无",
      teamCapacity: "无",
    },
    "6889e44b5353f300300e6cb6": {
      id: "6889e44b5353f300300e6cb6",
      avatar: "/images/user/user-01.jpg",
      username: "昱云",
      userId: "UID3232915",
      userEmail: "6a7accc683e1094405ad1",
      phone: "173****165",
      region: "中国",
      registerSource: "公司 APP",
      registerTime: "2025-07-18 18:07",
      status: "active",
      storageUsed: "0 MB",
      storageLimit: "永久有效",
      personalSpaceUsed: "0 MB",
      personalSpaceLimit: "500 MB",
      teamStatus: "未创建",
      teamExpiry: "-",
      teamId: "无",
      teamCapacity: "无",
    },
  };

  return userDataMap[userId] || null;
};

export default function UserInfoDetail() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [userDetail, setUserDetail] = useState<UserDetailData | null>(null);
  const [loading, setLoading] = useState(true);

  const { copyToClipboard } = useCopyToClipboard({
    successMessage: t("pages.userInfoDetail.copied"),
    errorMessage: t("pages.userInfoDetail.copyFailed"),
  });

  const userId = searchParams.get("id");

  useEffect(() => {
    if (!userId) {
      toast.error(t("pages.userInfoDetail.invalidUserId"));
      navigate(ROUTES.USER.INFO);
      return;
    }
    // 模拟API调用
    setTimeout(() => {
      const userData = getMockUserDetailData(userId);
      setUserDetail(userData);
      setLoading(false);
    }, 500);
  }, [userId, navigate, t]);

  const handleGoBack = () => {
    navigate(ROUTES.USER.INFO);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-lg">{t("common.loading")}</div>
      </div>
    );
  }

  if (!userDetail) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-lg text-red-500">{t("pages.userInfoDetail.userNotFound")}</div>
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title={t("pages.userInfoDetail.title")}
        description={t("pages.userInfoDetail.description")}
      />
      <PageBreadcrumb pageTitle={t("pages.userInfoDetail.breadcrumb")} />

      <div className="space-y-6">
        {/* 返回按钮 */}
        <div className="flex items-center gap-3">
          <Button
            onClick={handleGoBack}
            variant="outline"
            startIcon={<AngleLeftIcon className="size-4" />}
          >
            {t("common.goBack")}
          </Button>
        </div>

        {/* 用户基本信息 */}
        <ComponentCard title={t("pages.userInfoDetail.basicInfo")}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 用户头像和用户名 */}
            <div className="md:col-span-2 lg:col-span-1">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 overflow-hidden rounded-full">
                  <img
                    width={64}
                    height={64}
                    src={userDetail.avatar}
                    alt={userDetail.username}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {userDetail.username}
                  </h3>
                  <Badge size="sm" color={userDetail.status === "active" ? "success" : "error"}>
                    {t(`pages.userInfoDetail.status.${userDetail.status}`)}
                  </Badge>
                </div>
              </div>
            </div>

            {/* 用户ID */}
            <div>
              <Label>{t("pages.userInfoDetail.userId")}</Label>
              <div className="mt-1">
                <span
                  className="text-brand-500 hover:underline cursor-pointer font-medium"
                  onClick={() => copyToClipboard(userDetail.userId)}
                >
                  {userDetail.userId}
                </span>
              </div>
            </div>

            {/* 用户邮箱 */}
            <div>
              <Label>{t("pages.userInfoDetail.userEmail")}</Label>
              <div className="mt-1">
                <span
                  className="text-brand-500 hover:underline cursor-pointer"
                  onClick={() => copyToClipboard(userDetail.userEmail)}
                >
                  {userDetail.userEmail}
                </span>
              </div>
            </div>

            {/* 手机号 */}
            <div>
              <Label>{t("pages.userInfoDetail.phone")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">{userDetail.phone}</div>
            </div>

            {/* 地区 */}
            <div>
              <Label>{t("pages.userInfoDetail.region")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">{userDetail.region}</div>
            </div>

            {/* 注册来源 */}
            <div>
              <Label>{t("pages.userInfoDetail.registerSource")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">
                {userDetail.registerSource}
              </div>
            </div>

            {/* 注册时间 */}
            <div>
              <Label>{t("pages.userInfoDetail.registerTime")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">{userDetail.registerTime}</div>
            </div>

            {/* 认证状态 */}
            <div>
              <Label>{t("pages.userInfoDetail.verificationStatus")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">
                {t("pages.userInfoDetail.unverified")}
              </div>
            </div>
          </div>
        </ComponentCard>

        {/* 内用信息 */}
        <ComponentCard title={t("pages.userInfoDetail.storageInfo")}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* 用户身份 */}
            <div>
              <Label>{t("pages.userInfoDetail.userType")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">
                {t("pages.userInfoDetail.freeUser")}
              </div>
            </div>

            {/* 有效期 */}
            <div>
              <Label>{t("pages.userInfoDetail.validity")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">{userDetail.storageLimit}</div>
            </div>

            {/* 个人空间使用情况 */}
            <div>
              <Label>{t("pages.userInfoDetail.personalSpaceUsed")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">
                {userDetail.personalSpaceUsed}
              </div>
            </div>

            {/* 个人空间总容量 */}
            <div>
              <Label>{t("pages.userInfoDetail.personalSpaceLimit")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">
                {userDetail.personalSpaceLimit}
              </div>
            </div>
          </div>
        </ComponentCard>

        {/* 团队信息 */}
        <ComponentCard title={t("pages.userInfoDetail.teamInfo")}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* 团队状态 */}
            <div>
              <Label>{t("pages.userInfoDetail.teamStatus")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">{userDetail.teamStatus}</div>
            </div>

            {/* 有效期 */}
            <div>
              <Label>{t("pages.userInfoDetail.teamExpiry")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">{userDetail.teamExpiry}</div>
            </div>

            {/* 团队制作容量ID */}
            <div>
              <Label>{t("pages.userInfoDetail.teamId")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">{userDetail.teamId}</div>
            </div>

            {/* 团队制作容量 */}
            <div>
              <Label>{t("pages.userInfoDetail.teamCapacity")}</Label>
              <div className="mt-1 text-gray-700 dark:text-gray-300">{userDetail.teamCapacity}</div>
            </div>
          </div>
        </ComponentCard>
      </div>
    </>
  );
}
