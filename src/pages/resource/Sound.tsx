import { useState } from "react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import PageBreadcrumb from "@components/common/PageBreadCrumb";
import PageMeta from "@components/common/PageMeta";
import ComponentCard from "@components/common/ComponentCard";
import Label from "@components/form/Label";
import InputField from "@components/form/input/InputField";
import Select from "@components/ui/select/Selection";
import DatePicker from "@components/form/date-picker";
import Button from "@components/ui/button/Button";
import Badge from "@components/ui/badge/Badge";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@components/ui/table";
import { ChevronUpIcon, AudioIcon, PencilIcon, CopyIcon } from "@assets/icons";
import { useCopyToClipboard } from "@hooks/useCopyToClipboard";
import { useModal } from "@hooks/useModal";
import CreateSoundDialog, { SoundFormData } from "@components/features/sound/CreateSoundDialog";

// 配音数据类型定义
interface SoundData {
  id: string;
  cover: string;
  voiceName: string;
  serviceProvider: "MEGA" | "Azure" | "AWS" | "Google";
  usageCount: number;
  favoriteCount: number;
  createDate: string;
}

// 模拟数据
const mockSoundData: SoundData[] = [
  {
    id: "V001",
    cover: "/images/user/user-01.jpg",
    voiceName: "琨晟",
    serviceProvider: "MEGA",
    usageCount: 0,
    favoriteCount: 0,
    createDate: "2025-07-17 18:01:58",
  },
  {
    id: "V002",
    cover: "/images/user/user-02.jpg",
    voiceName: "汤嘉怡",
    serviceProvider: "MEGA",
    usageCount: 0,
    favoriteCount: 0,
    createDate: "2025-07-17 18:01:18",
  },
  {
    id: "V003",
    cover: "/images/user/user-03.jpg",
    voiceName: "宾霸",
    serviceProvider: "MEGA",
    usageCount: 0,
    favoriteCount: 0,
    createDate: "2025-07-17 18:00:25",
  },
  {
    id: "V004",
    cover: "/images/user/user-04.jpg",
    voiceName: "尹琳",
    serviceProvider: "MEGA",
    usageCount: 12,
    favoriteCount: 5,
    createDate: "2025-07-17 17:59:23",
  },
  {
    id: "V005",
    cover: "/images/user/user-05.jpg",
    voiceName: "万颖",
    serviceProvider: "MEGA",
    usageCount: 2,
    favoriteCount: 1,
    createDate: "2025-07-17 17:58:01",
  },
  {
    id: "V006",
    cover: "/images/user/user-06.jpg",
    voiceName: "雅琪",
    serviceProvider: "MEGA",
    usageCount: 2,
    favoriteCount: 0,
    createDate: "2025-07-17 17:48:13",
  },
  {
    id: "V007",
    cover: "/images/user/user-07.jpg",
    voiceName: "千络樱雄",
    serviceProvider: "MEGA",
    usageCount: 3,
    favoriteCount: 2,
    createDate: "2025-07-17 17:45:51",
  },
  {
    id: "V008",
    cover: "/images/user/user-08.jpg",
    voiceName: "亲和雪重",
    serviceProvider: "MEGA",
    usageCount: 0,
    favoriteCount: 0,
    createDate: "2025-07-17 17:41:26",
  },
  {
    id: "V009",
    cover: "/images/user/user-09.jpg",
    voiceName: "自然夏琪",
    serviceProvider: "MEGA",
    usageCount: 5,
    favoriteCount: 2,
    createDate: "2025-07-17 17:35:26",
  },
];

export default function Sound() {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const { copyToClipboard } = useCopyToClipboard({
    successMessage: t("pages.sound.copied"),
    errorMessage: t("pages.sound.copyFailed"),
  });
  const {
    isOpen: isCreateDialogOpen,
    openModal: openCreateDialog,
    closeModal: closeCreateDialog,
  } = useModal();

  const [queryForm, setQueryForm] = useState({
    conditionField: "",
    conditionValue: "",
    startDate: "",
    endDate: "",
    serviceProvider: "",
    sortBy: "",
  });

  // 条件查询字段选项
  const conditionFieldOptions = [
    { value: "voiceId", label: t("pages.sound.conditionField.voiceId") },
    { value: "voiceName", label: t("pages.sound.conditionField.voiceName") },
    { value: "userEmail", label: t("pages.sound.conditionField.userEmail") },
  ];

  // 服务商选项
  const serviceProviderOptions = [
    { value: "mega", label: t("pages.sound.serviceProviders.mega") },
    { value: "azure", label: t("pages.sound.serviceProviders.azure") },
    { value: "aws", label: t("pages.sound.serviceProviders.aws") },
    { value: "google", label: t("pages.sound.serviceProviders.google") },
  ];

  // 排序选项
  const sortOptions = [
    { value: "mostPopular", label: t("pages.sound.sortOptions.mostPopular") },
    { value: "newest", label: t("pages.sound.sortOptions.newest") },
    { value: "aToZ", label: t("pages.sound.sortOptions.aToZ") },
    { value: "zToA", label: t("pages.sound.sortOptions.zToA") },
  ];

  const handleQuery = () => {
    console.log("Query:", queryForm);
    toast.success(t("pages.userInfo.queryComplete"), {
      duration: 2000,
      icon: "🔍",
    });
  };

  const handleReset = () => {
    setQueryForm({
      conditionField: "",
      conditionValue: "",
      startDate: "",
      endDate: "",
      serviceProvider: "",
      sortBy: "",
    });

    toast(t("pages.userInfo.formReset"), {
      icon: "🔄",
      duration: 1500,
    });
  };

  const handleConditionFieldChange = (value: string) => {
    setQueryForm({ ...queryForm, conditionField: value });
  };

  const handleServiceProviderChange = (value: string) => {
    setQueryForm({ ...queryForm, serviceProvider: value });
  };

  const handleSortChange = (value: string) => {
    setQueryForm({ ...queryForm, sortBy: value });
  };

  const handlePlay = (voiceName: string) => {
    toast(`播放 ${voiceName}`, {
      icon: "🎵",
      duration: 2000,
    });
  };

  const handleEdit = (id: string, voiceName: string) => {
    toast(`编辑 ${voiceName} (${id})`, {
      icon: "✏️",
      duration: 2000,
    });
  };

  const handleCreateNew = () => {
    openCreateDialog();
  };

  const handleSaveSound = (data: SoundFormData) => {
    console.log("Create new sound:", data);
    toast.success(t("pages.sound.createSuccess"), {
      duration: 2000,
      icon: "✨",
    });
    // TODO: 这里处理保存逻辑，比如调用API
  };

  const getServiceProviderBadgeColor = (provider: string) => {
    switch (provider) {
      case "MEGA":
        return "success";
      case "Azure":
        return "info";
      case "AWS":
        return "warning";
      case "Google":
        return "primary";
      default:
        return "light";
    }
  };

  return (
    <>
      <PageMeta title={t("pages.sound.title")} description={t("pages.sound.description")} />
      <PageBreadcrumb pageTitle={t("pages.sound.breadcrumb")} />

      <div className="space-y-6">
        {/* 查询条件区域 */}
        <ComponentCard title="">
          <div className={`space-y-4 ${collapsed ? "hidden" : ""}`}>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 条件查询 - 字段选择 + 输入框 */}
              <div className="md:col-span-2">
                <Label htmlFor="condition">{t("pages.sound.conditionQuery")}</Label>
                <div className="flex gap-2">
                  <div className="w-36">
                    <Select
                      options={conditionFieldOptions}
                      placeholder={t("pages.sound.conditionField.select")}
                      onChange={handleConditionFieldChange}
                      value={queryForm.conditionField}
                      size="md"
                      clearable
                    />
                  </div>
                  <div className="flex-1">
                    <InputField
                      type="text"
                      id="condition-value"
                      placeholder={t("pages.sound.conditionValue.placeholder")}
                      value={queryForm.conditionValue}
                      onChange={e =>
                        setQueryForm({
                          ...queryForm,
                          conditionValue: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              {/* 创建日期 - 开始日期 */}
              <div>
                <DatePicker
                  id="start-date"
                  label={`${t("pages.sound.createDate")} (开始)`}
                  placeholder="2025-06-17"
                  onChange={(_, currentDateString) => {
                    setQueryForm({
                      ...queryForm,
                      startDate: currentDateString,
                    });
                  }}
                />
              </div>

              {/* 创建日期 - 结束日期 */}
              <div>
                <DatePicker
                  id="end-date"
                  label={`${t("pages.sound.createDate")} (结束)`}
                  placeholder="2025-07-18"
                  onChange={(_, currentDateString) => {
                    setQueryForm({ ...queryForm, endDate: currentDateString });
                  }}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 服务商 */}
              <div>
                <Label>{t("pages.sound.serviceProvider")}</Label>
                <Select
                  options={serviceProviderOptions}
                  placeholder={t("pages.sound.serviceProviders.all")}
                  onChange={handleServiceProviderChange}
                  value={queryForm.serviceProvider}
                  size="md"
                  clearable
                />
              </div>

              {/* 排序方式 */}
              <div>
                <Label>{t("pages.sound.sortBy")}</Label>
                <Select
                  options={sortOptions}
                  placeholder={t("pages.sound.sortBy")}
                  onChange={handleSortChange}
                  value={queryForm.sortBy}
                  size="md"
                  clearable
                />
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex items-center justify-between mt-6">
            <div className="flex items-center gap-3">
              <Button onClick={handleReset} variant="outline">
                {t("common.reset")}
              </Button>
              <Button onClick={handleQuery}>{t("pages.sound.query")}</Button>
              <Button
                onClick={() => setCollapsed(!collapsed)}
                variant="outline"
                endIcon={
                  <ChevronUpIcon
                    className={`size-4 transition-transform ${collapsed ? "rotate-180" : ""}`}
                  />
                }
              >
                {t("pages.sound.collapse")}
              </Button>
            </div>
            <div>
              <Button onClick={handleCreateNew}>{t("pages.sound.createNew")}</Button>
            </div>
          </div>
        </ComponentCard>

        {/* 配音列表表格 */}
        <ComponentCard title="">
          <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
            <div className="max-w-full overflow-x-auto">
              <Table>
                <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                  <TableRow>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.sound.voiceName")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.sound.serviceProvider")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.sound.preview")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.sound.usageCount")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.sound.favoriteCount")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.sound.createDate")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.sound.actions")}
                    </TableCell>
                  </TableRow>
                </TableHeader>

                <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                  {mockSoundData.map(sound => (
                    <TableRow key={sound.id}>
                      <TableCell className="px-5 py-4 sm:px-6 text-start">
                        <div className="flex items-center gap-3">
                          <div className="relative w-10 h-10 overflow-hidden rounded-full">
                            <img
                              width={40}
                              height={40}
                              src={sound.cover}
                              alt={sound.voiceName}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                <AudioIcon className="w-3 h-3 text-white" />
                              </div>
                            </div>
                          </div>
                          <span className="font-medium text-gray-800 text-theme-sm dark:text-white/90">
                            {sound.voiceName}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <Badge
                          size="sm"
                          color={getServiceProviderBadgeColor(sound.serviceProvider)}
                        >
                          {sound.serviceProvider}
                        </Badge>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <button
                          className="text-brand-500 hover:underline cursor-pointer"
                          onClick={() => handlePlay(sound.voiceName)}
                        >
                          {t("pages.sound.play")}
                        </button>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {sound.usageCount}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {sound.favoriteCount}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {sound.createDate}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <div className="flex items-center gap-2">
                          <button
                            className="text-brand-500 hover:underline flex items-center gap-1"
                            onClick={() => handleEdit(sound.id, sound.voiceName)}
                          >
                            <PencilIcon className="w-3 h-3" />
                            {t("pages.sound.edit")}
                          </button>
                          <span className="text-gray-300">|</span>
                          <button
                            className="text-brand-500 hover:underline flex items-center gap-1"
                            onClick={() => copyToClipboard(sound.id)}
                          >
                            <CopyIcon className="w-3 h-3" />
                            {t("pages.sound.copyId")}
                          </button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </ComponentCard>
      </div>

      {/* 新增配音对话框 */}
      <CreateSoundDialog
        isOpen={isCreateDialogOpen}
        onClose={closeCreateDialog}
        onSave={handleSaveSound}
      />
    </>
  );
}
