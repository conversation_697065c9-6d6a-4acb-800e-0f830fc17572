import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import toast from "react-hot-toast";
import PageBreadcrumb from "@components/common/PageBreadCrumb";
import PageMeta from "@components/common/PageMeta";
import ComponentCard from "@components/common/ComponentCard";
import Label from "@components/form/Label";
import InputField from "@components/form/input/InputField";
import Select from "@components/ui/select/Selection";
import DatePicker from "@components/form/date-picker";
import Button from "@components/ui/button/Button";
import Badge from "@components/ui/badge/Badge";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@components/ui/table";
import { ChevronUpIcon, ChevronDownIcon, TrashBinIcon } from "@assets/icons";
import { useCopyToClipboard } from "@hooks/useCopyToClipboard";
import { Popover, PopoverItem } from "@components/ui/popover/Popover";
import CustomPagination from "@components/ui/pagination/CustomPagination";
import { generateRoute } from "@/constants/routes";

// 用户数据类型定义
interface UserData {
  id: number;
  avatar: string;
  username: string;
  userId: string;
  userEmail: string;
  region: string;
  registerSource: string;
  registerTime: string;
  status: "active" | "inactive";
}

// 模拟数据
const mockUserData: UserData[] = [
  {
    id: 1,
    avatar: "/images/user/user-01.jpg",
    username: "昱云",
    userId: "**********",
    userEmail: "<EMAIL>",
    region: "中国",
    registerSource: "邀请码",
    registerTime: "2025-07-18 14:25:43",
    status: "active",
  },
  {
    id: 2,
    avatar: "/images/user/user-02.jpg",
    username: "张三",
    userId: "UID3232183",
    userEmail: "<EMAIL>",
    region: "新加坡",
    registerSource: "邀请码",
    registerTime: "2025-07-18 14:25:43",
    status: "active",
  },
  {
    id: 3,
    avatar: "/images/user/user-03.jpg",
    username: "李四",
    userId: "UID3232182",
    userEmail: "<EMAIL>",
    region: "美国",
    registerSource: "邀请码",
    registerTime: "2025-07-18 14:25:43",
    status: "active",
  },
  {
    id: 4,
    avatar: "/images/user/user-04.jpg",
    username: "王五",
    userId: "UID3232181",
    userEmail: "<EMAIL>",
    region: "英国",
    registerSource: "邀请码",
    registerTime: "2025-07-18 14:25:19",
    status: "active",
  },
  {
    id: 5,
    avatar: "/images/user/user-05.jpg",
    username: "小风",
    userId: "UID3232180",
    userEmail: "<EMAIL>",
    region: "加拿大",
    registerSource: "邀请码",
    registerTime: "2025-07-18 14:25:18",
    status: "active",
  },
];

export default function UserInfo() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total] = useState(100); // 模拟总数据量
  const { copyToClipboard } = useCopyToClipboard({
    successMessage: t("pages.userInfo.copied"),
    errorMessage: t("pages.userInfo.copyFailed"),
  });

  const [queryForm, setQueryForm] = useState({
    conditionField: "",
    conditionValue: "",
    startDate: "",
    endDate: "",
    region: "",
    registerSource: "",
  });

  // 条件查询字段选项
  const conditionFieldOptions = [
    { value: "username", label: t("pages.userInfo.conditionField.username") },
    { value: "userId", label: t("pages.userInfo.conditionField.userId") },
    { value: "userEmail", label: t("pages.userInfo.conditionField.userEmail") },
    {
      value: "inviteCode",
      label: t("pages.userInfo.conditionField.inviteCode"),
    },
  ];

  // 地区选项
  const regionOptions = [
    { value: "china", label: t("pages.userInfo.regions.china") },
    { value: "singapore", label: t("pages.userInfo.regions.singapore") },
    { value: "usa", label: t("pages.userInfo.regions.usa") },
    { value: "uk", label: t("pages.userInfo.regions.uk") },
    { value: "canada", label: t("pages.userInfo.regions.canada") },
    { value: "hongkong", label: t("pages.userInfo.regions.hongkong") },
  ];

  // 注册来源选项
  const sourceOptions = [{ value: "inviteCode", label: t("pages.userInfo.sources.inviteCode") }];

  const handleQuery = () => {
    console.log("Query:", queryForm);
    // todo  这里处理查询逻辑
    // 演示直接使用 toast 函数（无需 hook）
    toast.success("查询完成！", {
      duration: 2000,
      icon: "🔍",
    });
  };

  const handleReset = () => {
    setQueryForm({
      conditionField: "",
      conditionValue: "",
      startDate: "",
      endDate: "",
      region: "",
      registerSource: "",
    });

    // 演示 toast 消息
    toast("表单已重置", {
      icon: "🔄",
      duration: 1500,
    });
  };

  const handleConditionFieldChange = (value: string) => {
    setQueryForm({ ...queryForm, conditionField: value });
  };

  const handleRegionChange = (value: string) => {
    setQueryForm({ ...queryForm, region: value });
  };

  const handleSourceChange = (value: string) => {
    setQueryForm({ ...queryForm, registerSource: value });
  };

  const handleUserDelete = (userId: string, username: string) => {
    console.log("删除用户:", userId, username);
    toast.success(`删除用户 ${username}`, {
      duration: 2000,
      icon: "🗑️",
    });
  };

  const handleUserBlock = (userId: string, username: string) => {
    console.log("封禁用户:", userId, username);
    toast.success(`封禁用户 ${username}`, {
      duration: 2000,
      icon: "🚫",
    });
  };

  const handleUserReset = (userId: string, username: string) => {
    console.log("重置密码:", userId, username);
    toast.success(`重置用户 ${username} 的密码`, {
      duration: 2000,
      icon: "🔑",
    });
  };

  const handleUserDetails = (userId: string) => {
    // 跳转到用户详情页面，传递用户ID
    navigate(generateRoute.userDetail(userId));
  };

  // 分页处理函数
  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
    console.log("分页变化:", { page, size });
    // 这里可以调用 API 获取新的数据
  };

  // 生成用户操作菜单项
  const getUserActionItems = (user: UserData): PopoverItem[] => [
    {
      label: t("pages.userInfo.userActions.blockUser"),
      icon: <span className="size-4 text-red-500">🚫</span>,
      onClick: () => handleUserBlock(user.userId, user.username),
    },
    {
      label: t("pages.userInfo.userActions.deleteUser"),
      icon: <TrashBinIcon className="size-4 text-red-500" />,
      onClick: () => handleUserDelete(user.userId, user.username),
    },
    {
      label: t("pages.userInfo.userActions.unblockUser"),
      icon: <span className="size-4 text-blue-500">🔑</span>,
      onClick: () => handleUserReset(user.userId, user.username),
    },
  ];

  return (
    <>
      <PageMeta title={t("pages.userInfo.title")} description={t("pages.userInfo.description")} />
      <PageBreadcrumb pageTitle={t("pages.userInfo.breadcrumb")} />

      <div className="space-y-6">
        {/* 查询条件区域 */}
        <ComponentCard title="">
          <div className={`space-y-4 ${collapsed ? "hidden" : ""}`}>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 条件查询 - 字段选择 + 输入框 */}
              <div className="md:col-span-2">
                <Label htmlFor="condition">{t("pages.userInfo.conditionQuery")}</Label>
                <div className="flex gap-2">
                  <div className="w-36">
                    <Select
                      options={conditionFieldOptions}
                      placeholder={t("pages.userInfo.conditionField.select")}
                      onChange={handleConditionFieldChange}
                      value={queryForm.conditionField}
                      size="md"
                    />
                  </div>
                  <div className="flex-1">
                    <InputField
                      type="text"
                      id="condition-value"
                      placeholder={t("pages.userInfo.conditionValue.placeholder")}
                      value={queryForm.conditionValue}
                      onChange={e =>
                        setQueryForm({
                          ...queryForm,
                          conditionValue: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              {/* 注册时间 - 开始日期 */}
              <div>
                <DatePicker
                  id="start-date"
                  label={`${t("pages.userInfo.registerTime")} (开始)`}
                  placeholder="2025-06-17"
                  onChange={(_dates, currentDateString) => {
                    setQueryForm({
                      ...queryForm,
                      startDate: currentDateString,
                    });
                  }}
                />
              </div>

              {/* 注册时间 - 结束日期 */}
              <div>
                <DatePicker
                  id="end-date"
                  label={`${t("pages.userInfo.registerTime")} (结束)`}
                  placeholder="2025-07-18"
                  onChange={(_dates, currentDateString) => {
                    setQueryForm({ ...queryForm, endDate: currentDateString });
                  }}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 地区 */}
              <div>
                <Label>{t("pages.userInfo.region")}</Label>
                <Select
                  options={regionOptions}
                  placeholder={t("pages.userInfo.regions.all")}
                  onChange={handleRegionChange}
                  value={queryForm.region}
                  size="md"
                  clearable
                />
              </div>

              {/* 注册来源 */}
              <div>
                <Label>{t("pages.userInfo.registerSource")}</Label>
                <Select
                  options={sourceOptions}
                  placeholder={t("pages.userInfo.sources.all")}
                  onChange={handleSourceChange}
                  value={queryForm.registerSource}
                  size="md"
                  clearable
                />
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex items-center gap-3 mt-6">
            <Button onClick={handleReset} variant="outline">
              {t("common.reset")}
            </Button>
            <Button onClick={handleQuery}>{t("pages.userInfo.query")}</Button>
            <Button
              onClick={() => setCollapsed(!collapsed)}
              variant="outline"
              endIcon={
                <ChevronUpIcon
                  className={`size-4 transition-transform ${collapsed ? "rotate-180" : ""}`}
                />
              }
            >
              {t("pages.userInfo.collapse")}
            </Button>
          </div>
        </ComponentCard>

        {/* 用户列表表格 */}
        <ComponentCard title="">
          <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
            <div className="max-w-full overflow-x-auto">
              <Table>
                <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                  <TableRow>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userInfo.username")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userInfo.userId")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userInfo.userEmail")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userInfo.region")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userInfo.registerSource")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userInfo.registerTime")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userInfo.status")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userInfo.actions")}
                    </TableCell>
                  </TableRow>
                </TableHeader>

                <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                  {mockUserData.map(user => (
                    <TableRow key={user.id}>
                      <TableCell className="px-5 py-4 sm:px-6 text-start">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 overflow-hidden rounded-full">
                            <img
                              width={32}
                              height={32}
                              src={user.avatar}
                              alt={user.username}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <span className="font-medium text-gray-800 text-theme-sm dark:text-white/90">
                            {user.username}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span
                          className="text-brand-500 hover:underline cursor-pointer"
                          onClick={() => copyToClipboard(user.userId)}
                        >
                          {user.userId}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span
                          className="text-brand-500 hover:underline cursor-pointer"
                          onClick={() => copyToClipboard(user.userEmail)}
                        >
                          {user.userEmail}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {user.region}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {user.registerSource}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {user.registerTime}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        <Badge size="sm" color={user.status === "active" ? "success" : "error"}>
                          {t("pages.userInfo.active")}
                        </Badge>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <div className="flex items-center gap-2">
                          <button
                            className="text-brand-500 hover:underline"
                            onClick={() => handleUserDetails(user.userId)}
                          >
                            {t("pages.userInfo.details")}
                          </button>
                          <span className="text-gray-300">|</span>
                          <Popover
                            trigger={
                              <button className="text-brand-500 hover:underline flex items-center gap-1">
                                {t("pages.userInfo.userActions.more")}
                                <ChevronDownIcon className="size-3" />
                              </button>
                            }
                            items={getUserActionItems(user)}
                            triggerType="hover"
                            placement="bottom-right"
                            hoverDelay={150}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* 分页组件 */}
            <div className="m-6">
              <CustomPagination
                current={currentPage}
                total={total}
                pageSize={pageSize}
                onChange={handlePageChange}
                showQuickJumper={true}
                showSizeChanger={true}
                showTotal={true}
                pageSizeOptions={["10", "20", "50", "100"]}
                size="small"
                className="border-t border-gray-200 pt-4 dark:border-gray-800"
              />
            </div>
          </div>
        </ComponentCard>
      </div>
    </>
  );
}
